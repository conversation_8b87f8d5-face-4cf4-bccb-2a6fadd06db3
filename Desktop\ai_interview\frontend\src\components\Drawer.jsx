import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "react-icons/lu";
import { useState, useRef } from "react";

const Drawer = ({ isOpen, onClose, title, children }) => {
  const [copied, setCopied] = useState(false);
  const contentRef = useRef(null);

  const handleCopy = () => {
    const text = contentRef.current?.innerText || "";
    if (text) {
      navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  return (
    <>
      {/* Background Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/40 z-30 transition-opacity"
          onClick={onClose}
        ></div>
      )}

      {/* Drawer */}
      <div
        className={`fixed top-[60px] right-5 z-40 h-[calc(100dvh-70px)] flex flex-col bg-white w-full md:w-[40vw] shadow-2xl shadow-cyan-800/20 border-l border-gray-200 transform transition-transform duration-300 ease-in-out
        ${isOpen ? "translate-x-0" : "translate-x-full"}`}
        tabIndex="-1"
        aria-labelledby="drawer-right-label"
      >
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-b border-gray-200 bg-gray-50">
          <h5
            id="drawer-right-label"
            className="text-base font-semibold text-gray-900"
          >
            {title}
          </h5>

          <button
            type="button"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-900 rounded-lg w-8 h-8 flex items-center justify-center hover:bg-gray-200 transition"
          >
            <LuX className="text-lg" />
          </button>
        </div>

        {/* Body */}
        <div className="flex-1 overflow-y-auto px-4 py-3 custom-scrollbar">
          <div
            ref={contentRef}
            className="text-sm text-gray-700 whitespace-pre-wrap break-words"
          >
            {children}
          </div>
        </div>

        {/* Footer with Copy Button */}
        <div className="p-4 border-t border-gray-200 bg-gray-50 flex justify-end">
          <button
            onClick={handleCopy}
            className="flex items-center gap-2 px-4 py-2 rounded-xl  text-black hover:text-white hover:bg-cyan-700 shadow-md transition"
          >
            {copied ? <LuCheck /> : <LuCopy />}
          </button>
        </div>
      </div>

      {/* Hide Scrollbar */}
      <style jsx>{`
        .custom-scrollbar {
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* IE & Edge */
        }
        .custom-scrollbar::-webkit-scrollbar {
          display: none; /* Chrome, Safari, Opera */
        }
      `}</style>
    </>
  );
};

export default Drawer;
