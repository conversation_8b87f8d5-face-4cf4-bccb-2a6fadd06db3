Stack trace:
Frame         Function      Args
0007FFFF7600  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF6500) msys-2.0.dll+0x1FE8E
0007FFFF7600  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF78D8) msys-2.0.dll+0x67F9
0007FFFF7600  000210046832 (000210286019, 0007FFFF74B8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF7600  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF7600  000210068E24 (0007FFFF7610, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF78E0  00021006A225 (0007FFFF7610, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE472A0000 ntdll.dll
7FFE450E0000 KERNEL32.DLL
7FFE448A0000 KERNELBASE.dll
7FFE47010000 USER32.dll
7FFE44650000 win32u.dll
7FFE466F0000 GDI32.dll
7FFE44510000 gdi32full.dll
7FFE44460000 msvcp_win.dll
7FFE44EE0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE46630000 advapi32.dll
7FFE45030000 msvcrt.dll
7FFE46360000 sechost.dll
7FFE45760000 RPCRT4.dll
7FFE439C0000 CRYPTBASE.DLL
7FFE44800000 bcryptPrimitives.dll
7FFE46F20000 IMM32.DLL
